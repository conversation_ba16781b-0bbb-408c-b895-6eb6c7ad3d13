# 硬件仿真器 DPI-C 监视器设计准则

本文件总结了在设计基于 SystemVerilog 和 DPI-C 的硬件监视器时应遵循的最佳实践和设计准则。这些准则旨在提高监视器的可重用性、鲁棒性和可维护性。

## 1. 模块设计 (Module Design)

### 1.1. 支持多实例 (Multi-Instance Support)
- **准则**: 任何监视器模块都必须支持在设计中被多次实例化。
- **实施**:
    - 模块必须包含一个 `ID` 或 `INSTANCE_ID` `parameter`，用于在实例化时分配一个唯一的标识符。
    - 这个 `ID` 必须通过 DPI-C 调用传递给软件侧，以便日志和数据能够被正确地归属到对应的硬件实例。

### 1.2. 参数化位宽 (Parameterized Widths)
- **准则**: 严禁对地址或数据总线的位宽进行硬编码。
- **实施**:
    - 使用 `parameter` 来定义所有可变的位宽，例如 `ADDR_WIDTH` 和 `DATA_WIDTH`。
    - 这确保了监视器可以灵活地适应不同的设计需求。

## 2. DPI-C 接口设计 (DPI-C Interface Design)

### 2.1. 使用 `bit` 类型传递宽向量
- **准则**: 当通过 DPI-C 接口向 C++ 传递位宽较大（例如，超过32或64位）的向量时，必须使用 `bit` 类型。
- **原因**: SystemVerilog 的 `logic` 类型是四状态的 (0, 1, X, Z)，在传递给期望二状态 (`svBitVecVal`) 的 C++ 函数时，可能会导致数据损坏或截断。`bit` 类型是二状态的，可以确保数据在接口两侧的完整性。

### 2.2. 使用初始化调用传递静态信息
- **准则**: 使用一个一次性的初始化 DPI-C 调用来传递监视器的静态配置信息。
- **实施**:
    - 在仿真开始时（例如，在复位之后），调用一个专门的 `init` 函数，如 `h2s_monitor_init(ID, ADDR_WIDTH, DATA_WIDTH)`。
    - 这避免了在每一次数据传输的 DPI-C 调用中都重复传递这些静态参数，从而简化了通信协议并提高了效率。

### 2.3. 正确处理任意位宽数据
- **准则**: C++ 侧的 DPI-C 函数必须能够处理任意位宽的数据。
- **实施**:
    - C++ 函数签名应使用 `svBitVecVal*` 指针来接收数据，并附带一个 `int` 类型的参数来指定数据的位宽（以比特为单位）。
    - 避免使用固定大小的 C++ 类型（如 `unsigned int` 或 `uint64_t`）来接收可能超过其容量的数据。

### 2.4. 阻塞与非阻塞调用 (Blocking vs. Non-blocking Calls)
- **准则**: 理解并正确使用阻塞 (blocking) 和非阻塞 (non-blocking) 两种 `DPI-C` 调用风格。
- **实施**:
    - **阻塞调用 (默认)**: 硬件调用函数后会暂停执行，直到函数返回。适用于需要从软件侧立即获取返回值的场景。
    - **非阻塞调用**: 通过在 `import` 声明前添加 `(* is_nonblocking_dpi = 1 *)` 属性来实现。硬件调用后会立即继续执行，不会等待函数返回。
    - **限制**: 非阻塞 `DPI-C` 函数只允许有 `input` 方向的参数，不允许有 `output`、`inout` 或 `return` 值。

### 2.5. 函数命名规范 (Function Naming Convention)
- **准则**: 遵循统一的函数命名规范来区分 `DPI-C` 调用的方向。
- **实施**:
    - **`h2s_` (Hardware-to-Software)**: 用于 `import` 的 `DPI-C` 函数，表示数据或控制流从硬件到软件。
    - **`s2h_` (Software-to-Hardware)**: 用于 `export` 的 `DPI-C` 函数，表示数据或控制流从软件到硬件。

### 2.6. 硬件与软件的数据交换模式 (Data Exchange Patterns)
- **准则**: 根据交互的实时性要求，选择合适的数据交换模式。
- **实施**:
    - **模式一：阻塞调用 (Blocking Call)**:
        - **描述**: 最简单的数据交换模式。硬件调用一个阻塞的 `import` 函数，通过 `input` 发送数据，通过 `output` 或 `return` 接收返回值。
        - **适用场景**: 当软件侧可以立即计算并返回结果时。
    - **模式二：非阻塞回调 (Non-blocking Callback)**:
        - **描述**: 硬件调用一个非阻塞的 `import` 函数 (`h2s_`) 发送请求。软件侧在处理完请求后，调用一个 `export` 函数 (`s2h_`) 将结果异步地返回给硬件。
        - **适用场景**: 当软件侧需要较长时间处理请求，不希望阻塞硬件执行时。
    - **模式三：非阻塞轮询 (Non-blocking with Polling)**:
        - **描述**: 硬件首先调用一个非阻塞的 `import` 函数 (`h2s_`) 发送请求。然后，硬件侧会周期性地调用另一个 `import` 函数 (`h2s_polling`) 来轮询结果。当软件侧准备好数据后，它会在轮询函数内部调用一个 `export` 函数 (`s2h_`) 来返回结果；如果数据未就绪，则调用另一个 `export` 函数 (`s2h_`) 返回一个“无结果”的状态。
        - **适用场景**: 适用于软件侧处理时间不确定，且硬件需要主动查询结果的复杂场景。

## 3. 验证 (Verification)

### 3.1. 设计全面的测试用例
- **准则**: Testbench 必须覆盖各种边界情况和复杂场景。
- **实施**:
    - 除了基本的功能验证外，测试用例必须包括：
        - 使用监视器支持的最大位宽进行数据传输。
        - 对非对齐地址（例如，非4字节或8字节对齐）进行访问。
        - 在设计中实例化多个监视器，并验证它们可以并发、正确地工作。

## 4. SystemVerilog 编码规范 (SystemVerilog Coding Style)

### 4.1. 避免在 `function` 和 `always` 块中对同一变量赋值
- **准则**: 严禁在 `always` 块和 `function`（特别是 `DPI-C` import function）中对同一个变量进行赋值。
- **原因**: 这种做法会产生竞争条件 (race condition)，使得变量的最终值变得不可预测，这取决于仿真器的调度顺序。

### 4.2. 使用影子变量 (Shadow Variables) 来检测状态变化
- **准则**: 当需要在 `DPI-C` 调用中对 `always` 块驱动的信号进行采样时，应使用“影子变量”模式。
- **实施**: 在 `always` 块中驱动主信号。在 `DPI-C` 调用中，将主信号的值赋给一个“影子”变量。通过比较主信号和影子变量的差异，可以可靠地检测到状态的变化。

### 4.3. 在调用 DPI 的 `always` 块中避免使用异步复位
- **准则**: 任何包含 `DPI-C` 调用的 `always_ff` 块都必须使用同步复位。
- **原因**: 异步复位会使 `always` 块在时钟边沿之外被触发，这可能导致 `DPI-C` 调用在非预期的时刻执行，从而引发仿真与硬件行为不一致的问题。

### 4.4. 不要在 `always` 块中定义局部变量
- **准则**: 避免在 `always` 块内部声明局部变量。
- **原因**: 并非所有的综合和仿真工具都能很好地支持在 `always` 块中声明的局部变量。为了确保代码的可移植性和兼容性，所有变量都应在模块的顶层进行声明。