// fake_hbm3_pc.cc
#include "fake_hbm3_mm_bfm.h"
#include "svdpi.h"
#include <cstring>
#include <chrono>
#include <random>
#include <unordered_map>
#include <queue>

#ifdef NO_SIMULATOR
#define vpi_printf printf
#else
#include <vpi_user.h>
#endif

extern "C" {
    void h2s_hbm3_write(svBitVecVal* bfm_id, svBitVecVal* addr, 
                      svBitVecVal* data, int unsigned data_size,
                      svBitVecVal* mask) {
        uint64_t address = *reinterpret_cast<uint64_t*>(addr);
        FakeHBM3PC::getInstance().pcWrite(*(uint16_t*)bfm_id, address, data, data_size, mask);
    }

    void h2s_hbm3_read_request(svBitVecVal* bfm_id, svBitVecVal* addr,
                            int unsigned data_size) {
        uint64_t address = *reinterpret_cast<uint64_t*>(addr);
        svScope scope = svGetScope();
        FakeHBM3PC::getInstance().pcReadRequest(*(uint16_t*)bfm_id, address, data_size, scope);
    }

    void h2s_hbm3_read_data_request(svBitVecVal* bfm_id, svBitVecVal* left_space) {
        svScope scope = svGetScope();
        FakeHBM3PC::getInstance().readRequestData(*(uint16_t*)bfm_id, *(uint16_t*)left_space, scope);
    }

    void h2s_hbm3_init_bfm(svBitVecVal* bfm_id, int unsigned data_width) {
    }

    bool h2s_hbm3_read_data_blocking(svBitVecVal* bfm_id, svBitVecVal* rdata) {
        return FakeHBM3PC::getInstance().pcReadBlocking(*(uint16_t*)bfm_id, rdata);
    }
}

FakeHBM3PC& FakeHBM3PC::getInstance() {
    static FakeHBM3PC instance;
    return instance;
}

FakeHBM3PC::FakeHBM3PC() : running_(true) {
    data_thread_ = std::thread(&FakeHBM3PC::processDataRequests, this);
}

FakeHBM3PC::~FakeHBM3PC() {
    running_ = false;
    if (data_thread_.joinable()) {
        data_thread_.join();
    }
}

void FakeHBM3PC::pcWrite(uint16_t bfm_id,
                        uint64_t addr, const svBitVecVal* data, uint32_t data_size,
                        const svBitVecVal* mask) {
    std::lock_guard<std::mutex> lock(memory_mutex_);
    MemoryKey key{bfm_id, addr};
    
    // 确保内存区域存在
    auto& mem_data = memory_[key];
    if (mem_data.empty()) {
        mem_data.resize((data_size + 7) / 8, 0);
    }

    // 应用写操作和掩码
    const uint8_t* data_bytes = reinterpret_cast<const uint8_t*>(data);
    const uint8_t* mask_bytes = reinterpret_cast<const uint8_t*>(mask);
    
    for (uint32_t i = 0; i < (data_size + 7) / 8; ++i) {
        if (mask_bytes[i / 8] & (1 << (i % 8))) {
            mem_data[i] = data_bytes[i];
            // print out the address and data
            vpi_printf("[pcWrite] - bfm_id: %d, addr: %lx, data: %lx\n", 
                   bfm_id, addr+i, mem_data[i]);
        }
    }
}

void FakeHBM3PC::pcReadRequest(uint16_t bfm_id,
                              uint64_t addr, uint32_t data_size, const svScope scope) {
    // Take memory snapshot at request time
    std::vector<uint8_t> snapshot_data;
    {
        std::lock_guard<std::mutex> mem_lock(memory_mutex_);
        MemoryKey key{bfm_id, addr};
        auto it = memory_.find(key);
        if (it != memory_.end()) {
            snapshot_data = it->second;  // Create a copy of current data
        } else {
            vpi_printf("[pcReadRequest] - bfm_id: %d, addr: %lx, data_size: %d doesn't exist\n", 
                   bfm_id, addr, data_size);
            snapshot_data.resize((data_size + 7) / 8, 0);
        }
    }

    ReadRequest req{
        .bfm_id = bfm_id,
        .addr = addr,
        .data_size = data_size,
        .scope = scope,
        .data_ready = false,
        .data = std::move(snapshot_data),  // Store the snapshot
        .request_time = std::chrono::steady_clock::now()
    };

    vpi_printf("[pcReadRequest] - bfm_id: %d, addr: %lx, data_size: %d\n", 
           bfm_id, addr, data_size);
    for (int i = 0; i < req.data.size(); i++) {
        vpi_printf("data[%d]: %lx\n", i, req.data[i]);
    }

    std::lock_guard<std::mutex> lock(reads_mutex_);
    pending_reads_[bfm_id].push(req);
}

void FakeHBM3PC::readRequestData(uint16_t bfm_id,
                                uint16_t left_space, const svScope scope) {
    ReadRequest current_req;
    bool has_request = false;
    bool ready = false;

    {
        std::lock_guard<std::mutex> lock(reads_mutex_);
        auto it = pending_reads_.find(bfm_id);
        if (it != pending_reads_.end() && !it->second.empty()) {
            if (it->second.front().data_ready) {
                current_req = it->second.front();
                has_request = true;
                ready = true;
                it->second.pop();
                // 如果队列空了可以删除该key
                if (it->second.empty()) {
                    pending_reads_.erase(it);
                }
            }
        }
    }

    svScope old_scope = svSetScope(scope);

    if (has_request && ready) {
        vpi_printf("[readRequestData] - [%s] - bfm_id: %d, left_space: %d\n", 
            svGetNameFromScope(scope), bfm_id, left_space);
        s2h_hbm3_read_data_reply(reinterpret_cast<svBitVecVal*>(current_req.data.data()), 1);
    } else {
        s2h_hbm3_read_data_reply(reinterpret_cast<svBitVecVal*>(&bfm_id), 0);
    }

    svSetScope(old_scope);
}

bool FakeHBM3PC::pcReadBlocking(uint16_t bfm_id, svBitVecVal* rdata) {
    ReadRequest current_req;
    bool has_request = false;

    // This loop will block until data is ready for the oldest request.
    while (true) {
        {
            std::lock_guard<std::mutex> lock(reads_mutex_);
            auto it = pending_reads_.find(bfm_id);
            if (it != pending_reads_.end() && !it->second.empty()) {
                if (it->second.front().data_ready) {
                    current_req = it->second.front();
                    has_request = true;
                    it->second.pop();
                    if (it->second.empty()) {
                        pending_reads_.erase(it);
                    }
                    break; // Exit loop once ready data is found and popped
                }
            } else {
                // No pending requests for this bfm_id, cannot fulfill.
                return false;
            }
        }
        // Sleep for a short duration to prevent busy-waiting
        std::this_thread::sleep_for(std::chrono::microseconds(10));
    }

    if (has_request) {
        vpi_printf("[pcReadBlocking] - bfm_id: %d, addr: %lx, data found\n", bfm_id, current_req.addr);
        memcpy(rdata, current_req.data.data(), current_req.data.size());
        return true;
    }

    return false; // Should not be reached
}

void FakeHBM3PC::processDataRequests() {
    // Use random sleep duration up to 100us to simulate variable delay
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<int> dist(5, 100); // 1~100 us
    while (running_) {
        int sleep_us = dist(gen);
        std::this_thread::sleep_for(std::chrono::microseconds(sleep_us));
        simulateDataArrival();
    }
}

void FakeHBM3PC::simulateDataArrival() {
    std::lock_guard<std::mutex> lock(reads_mutex_);
    for (auto& pair : pending_reads_) {
        auto& queue = pair.second;
        if (!queue.empty() && !queue.front().data_ready) {
            queue.front().data_ready = true;
            // Debug logging
            vpi_printf("[simulateDataArrival] - bfm_id: %d, addr: %lx \n",
                   queue.front().bfm_id,
                   queue.front().addr);
            for (int i = 0; i < queue.front().data.size(); i++) {
                vpi_printf("data[%d]: %lx\n", i, queue.front().data[i]);
            }
        }
    }
}

void FakeHBM3PC::reset() {
    std::lock_guard<std::mutex> mem_lock(memory_mutex_);
    std::lock_guard<std::mutex> reads_lock(reads_mutex_);
    memory_.clear();
    pending_reads_.clear();
}
