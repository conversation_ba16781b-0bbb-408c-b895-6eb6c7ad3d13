// fake_hbm3_pc.h
#pragma once

#include <cstdint>
#include <unordered_map>
#include <vector>
#include <queue>
#include <mutex>
#include <thread>
#include <atomic>
#include "svdpi.h"
#include <chrono>

// Forward declarations of DPI-C export functions
extern "C" {
    extern void s2h_hbm3_read_data_reply(const svBitVecVal* data, const unsigned char valid);
}

class FakeHBM3PC {
public:
    static FakeHBM3PC& getInstance();
    ~FakeHBM3PC();

    // DPI-C interface functions
    void pcWrite(uint16_t bfm_id,
                uint64_t addr, const svBitVecVal* data, uint32_t data_size,
                const svBitVecVal* mask);
                
    void pcReadRequest(uint16_t bfm_id,
                      uint64_t addr, uint32_t data_size, const svScope scope);
                      
    void readRequestData(uint16_t bfm_id,
                        uint16_t left_space, const svScope scope);

    bool pcReadBlocking(uint16_t bfm_id, svBitVecVal* rdata);
    void reset();

private:
    FakeHBM3PC();
    FakeHBM3PC(const FakeHBM3PC&) = delete;
    FakeHBM3PC& operator=(const FakeHBM3PC&) = delete;

    // Memory storage
    struct MemoryKey {
        uint16_t bfm_id;
        uint64_t addr;

        bool operator==(const MemoryKey& other) const {
            return bfm_id == other.bfm_id &&
                   addr == other.addr;
        }
    };

    struct MemoryKeyHash {
        // Combines two hash values into one.
        static size_t CombineHash(size_t lhs, size_t rhs) {
            // This is the standard way to combine hashes in C++17.
            // 0x9e3779b9 is the golden ratio, helps to spread bits.
            return lhs ^ (rhs + 0x9e3779b9 + (lhs << 6) + (lhs >> 2));
        }

        size_t operator()(const MemoryKey& key) const {
            // Hash each field separately, then combine.
            size_t h1 = std::hash<uint16_t>{}(key.bfm_id);
            size_t h2 = std::hash<uint64_t>{}(key.addr);
            return CombineHash(h1, h2);
        }
    };

    // Read request tracking
    struct ReadRequest {
        uint16_t bfm_id;
        uint64_t addr;
        uint32_t data_size;
        svScope scope;
        bool data_ready;
        std::vector<uint8_t> data;
        // Add timestamp for debugging/tracking
        std::chrono::steady_clock::time_point request_time;
    };

    std::unordered_map<MemoryKey, std::vector<uint8_t>, MemoryKeyHash> memory_;
    std::mutex memory_mutex_;

    // Pending read requests queue
    std::unordered_map<uint16_t, std::queue<ReadRequest>> pending_reads_;
    std::mutex reads_mutex_;

    // Data processing thread
    std::thread data_thread_;
    std::atomic<bool> running_;

    void processDataRequests();
    void simulateDataArrival();  // simulate data arrival
};