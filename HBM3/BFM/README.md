# HBM3 BFM Specification

## 1. Overview

The HBM3 BFM (Bus Functional Model) provides a memory-mapped interface for interacting with an HBM3 memory model. It supports two modes of operation, selectable at compile time:

-   **Non-blocking**: Utilizes a FIFO for read data and asynchronous DPI-C calls for high throughput. This is the default implementation. **Features a hybrid approach with blocking fallback to meet HBM3 read latency requirements.**
-   **Blocking**: Uses direct, blocking DPI-C calls for simpler, sequential operations.

To select the blocking implementation, define the `MM_HBM3_SINGLE_STORAGE_BLOCKING` macro during compilation. Otherwise, the non-blocking implementation is used.

---

## 2. Non-blocking Implementation

The HBM3 BFM provides a non-blocking memory interface with read data buffering and flow control mechanisms. **To satisfy HBM3's strict read latency requirements, it implements a hybrid approach that combines non-blocking DPI-C calls with blocking fallback when timeout is imminent.**

### 2.1. Interface Parameters
```systemverilog
parameter HBM_ID = 0          // HBM instance ID
parameter CHANNEL_ID = 0      // Channel ID
parameter PC_ID = 0           // PC ID
parameter ADDR_WIDTH = 64     // Address width
parameter DATA_WIDTH = 256    // Data width
parameter DATA_BYTE_WIDTH = DATA_WIDTH/8  // Byte enable width
parameter FIFO_AWIDTH = 8     // FIFO address width (256 depth)
parameter FIFO_DEPTH = (1 << FIFO_AWIDTH) // FIFO depth
parameter MAX_OUTSTANDING_READS = 16      // Maximum pending reads
parameter BLOCKING_READ_THRESHOLD = 0     // Latency threshold for direct blocking reads
```

### 2.2. Read Latency Management

#### 2.2.1. Hybrid Approach
The BFM uses a **hybrid strategy** to meet HBM3's read latency constraints:

1. **Low-latency reads**: When `read_latency < BLOCKING_READ_THRESHOLD`, the BFM directly uses blocking DPI-C calls (`h2s_hbm3_read`) to guarantee immediate response.

2. **Normal non-blocking reads**: For higher latencies, the BFM uses non-blocking DPI-C calls (`h2s_hbm3_read_request`) to avoid halting hardware execution.

3. **Timeout fallback**: If a non-blocking read approaches its latency deadline, the BFM automatically switches to a blocking call (`h2s_hbm3_read_data_blocking`) to force immediate data return.

#### 2.2.2. Timeout Detection
```systemverilog
reg [31:0] cycle_counter                              // Hardware clock cycle counter
reg [31:0] pending_read_start_cycle[0:MAX_OUTSTANDING_READS-1]  // Start cycle for each pending read
reg [OUTSTANDING_READS_AWIDTH:0] pending_req_count    // Number of requests sent
reg [OUTSTANDING_READS_AWIDTH:0] pending_resp_count   // Number of responses received
```

**Timeout Logic**: 
- Each read request records its start cycle (`cycle_counter` value)
- Continuously monitors: `cycle_counter - start_cycle >= read_latency`
- When timeout is detected, immediately calls `h2s_hbm3_read_data_blocking`

### 2.3. FIFO Management

#### 2.3.1. FIFO Structure
```systemverilog
reg [DATA_WIDTH-1:0] read_fifo [0:FIFO_DEPTH-1]  // Data storage
bit [FIFO_AWIDTH:0] fifo_rptr                    // Read pointer
bit [FIFO_AWIDTH:0] fifo_wptr                    // Write pointer
bit [FIFO_AWIDTH:0] left_space                   // Available space
```

#### 2.3.2. Flow Control Counters
```systemverilog
reg [15:0] read_req_count              // Read requests sent
reg [15:0] read_resp_count             // Read responses received
reg [FIFO_AWIDTH:0] request_data_send_count    // Request data commands sent
reg [FIFO_AWIDTH:0] request_data_resp_count    // Request data responses received
```

#### 2.3.3. Flow Control Logic
- Pending request tracking: `read_req_count - read_resp_count`
- Pending data requests: `request_data_send_count - request_data_resp_count`
- Available space calculation: `left_space - pending_request_data_count`
- Request permission: `available_space > 1`

### 2.3. DPI-C Interface

#### 2.3.1. Write Operation
```c
void h2s_hbm3_write(
    input bit [15:0] bfm_id,
    input bit [64-1:0] addr,
    input bit [DATA_WIDTH-1:0] data,
    input int unsigned data_size,
    input bit [DATA_BYTE_WIDTH-1:0] mask
);
```

#### 2.3.2. Read Operations

**Low-latency blocking read:**
```c
void h2s_hbm3_read(
    input bit [15:0] bfm_id,
    input bit [63:0] addr,
    output bit [DATA_WIDTH-1:0] data
);
```

**Non-blocking read request:**
```c
void h2s_hbm3_read_request(
    input bit [15:0] bfm_id,
    input bit [64-1:0] addr,
    input int unsigned data_size
);
```

**Timeout fallback blocking read:**
```c
void h2s_hbm3_read_data_blocking(
    input bit [15:0] bfm_id,
    output bit [DATA_WIDTH-1:0] data
);
```

**Non-blocking data request:**
```c
void h2s_hbm3_read_data_request(
    input bit [15:0] bfm_id,
    input bit [15:0] left_space
);
```

#### 2.3.3. Callbacks
```systemverilog
// Callback for read data reply
function void s2h_hbm3_read_data_reply(input bit [DATA_WIDTH-1:0] data, input byte valid);
```

### 2.4. Operation Flow

#### 2.4.1. Read Operation Flow

**Case 1: Low-latency reads (`read_latency < BLOCKING_READ_THRESHOLD`)**
1. Read request received (`mm_ren` asserted)
2. Directly call `h2s_hbm3_read` (blocking)
3. Data returned immediately in next cycle
4. `mm_rvalid` asserted with data

**Case 2: Normal non-blocking reads**
1. Read request received (`mm_ren` asserted)
2. Record start cycle in `pending_read_start_cycle`
3. Call `h2s_hbm3_read_request` (non-blocking)
4. Increment `pending_req_count`
5. Periodically call `h2s_hbm3_read_data_request` when FIFO has space
6. C++ model calls `s2h_hbm3_read_data_reply` when data ready
7. Data stored in FIFO, `mm_rvalid` asserted when available

**Case 3: Timeout fallback**
1. Monitor oldest pending read: `cycle_counter - start_cycle >= read_latency`
2. When timeout detected, immediately call `h2s_hbm3_read_data_blocking`
3. Hardware clock halts until data returned
4. Data stored in FIFO, `pending_resp_count` incremented

### 2.5. Error Handling
- **FIFO Overflow**: If FIFO is full, incoming read data is discarded. Flow control logic aims to prevent this.
- **Latency Violation Prevention**: Timeout fallback mechanism ensures read latency requirements are always met.
- **Outstanding Read Limit**: Maximum of `MAX_OUTSTANDING_READS` concurrent reads to prevent resource exhaustion.

---

## 3. Blocking Implementation

The blocking implementation provides a simpler, sequential interface to the HBM3 memory model. It is enabled by defining the `MM_HBM3_SINGLE_STORAGE_BLOCKING` macro during compilation.

### 3.1. Interface Parameters
The interface parameters are the same as the non-blocking implementation, but `FIFO_AWIDTH` and `FIFO_DEPTH` are not used.

```systemverilog
parameter HBM_ID = 0          // HBM instance ID
parameter CHANNEL_ID = 0      // Channel ID
parameter PC_ID = 0           // PC ID
parameter ADDR_WIDTH = 64     // Address width
parameter DATA_WIDTH = 256    // Data width
parameter DATA_BYTE_WIDTH = DATA_WIDTH/8  // Byte enable width
```

### 3.2. Interface Ports
The interface ports are identical to the non-blocking implementation.

#### 3.2.1. Write Interface
```systemverilog
input  wire                           mm_clk
input  wire                           mm_rstn
input  wire                           mm_wen
input  wire [ADDR_WIDTH-1:0]         mm_waddr
input  wire [DATA_WIDTH-1:0]         mm_wdata
input  wire [DATA_BYTE_WIDTH-1:0]    mm_wmask
```

#### 3.2.2. Read Interface
```systemverilog
input  wire                           mm_ren
input  wire [ADDR_WIDTH-1:0]         mm_raddr
output reg  [DATA_WIDTH-1:0]         mm_rdata
output reg                           mm_rvalid
```

### 3.3. DPI-C Interface

#### 3.3.1. Write Operation
The write operation is a blocking call. The simulation will pause until the write operation is completed in the C model.

```c
void h2s_hbm3_write(
    input bit [15:0] bfm_id,
    input bit [ADDR_WIDTH-1:0] addr,
    input bit [DATA_WIDTH-1:0] data,
    input int unsigned data_size,
    input bit [DATA_BYTE_WIDTH-1:0] mask,
    output bit done
);
```

#### 3.3.2. Read Operation
The read operation is a blocking call. The simulation will pause until the data is returned from the C model.

```c
void h2s_hbm3_read(
    input bit [15:0] bfm_id,
    input bit [ADDR_WIDTH-1:0] addr,
    input int unsigned data_size,
    output bit [DATA_WIDTH-1:0] data
);
```

### 3.4. Operation Flow

#### 3.4.1. Write Operation
1.  A write request is received (`mm_wen` is asserted).
2.  The `h2s_hbm3_write` DPI-C function is called.
3.  The BFM waits until the `done` signal is asserted by the C model, indicating the write is complete.

#### 3.4.2. Read Operation
1.  A read request is received (`mm_ren` is asserted).
2.  The `h2s_hbm3_read` DPI-C function is called.
3.  The BFM waits for the function to return with the read data.
4.  The `mm_rdata` and `mm_rvalid` signals are updated in the next clock cycle. `mm_rvalid` is asserted for one clock cycle.

### 3.5. Reset Behavior
- `mm_rvalid` is deasserted.
- `mm_rdata` is cleared to '0'.
